/**
 * 滚动位置管理服务
 * 提供页面滚动位置的记录、恢复功能
 *
 * 功能特性：
 * - 简洁的API设计，只传递key和滚动位置
 * - 支持多页面独立管理滚动位置
 * - 内存存储，页面生命周期内有效
 * - 最小侵入性集成
 * - 支持最小滚动高度管理，智能处理tab切换滚动逻辑
 */

// 内存存储滚动位置数据
const scrollPositions = {}

// 内存存储最小滚动高度配置
const minScrollHeights = {}

// 内存存储当前滚动位置（用于智能恢复时的临时存储）
const currentScrollPositions = {}

/**
 * 记录滚动位置
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} scrollTop 滚动位置
 */
function setScrollPosition(key, scrollTop) {
  scrollPositions[key] = scrollTop
}

/**
 * 智能恢复滚动位置（支持最小滚动高度管理）
 * @param {string} key 目标位置标识
 * @param {Object} options 恢复选项
 * @param {number} options.duration 动画时长，默认0（无动画）
 * @param {string} options.currentKey 当前位置标识（可选，用于获取当前滚动状态）
 */
function restoreScrollPosition(key, options = {}) {
  const { duration = 0, currentKey } = options

  // 获取当前滚动位置
  let currentScroll = 0
  let currentMinHeight = 0

  if (currentKey) {
    // 如果提供了当前key，从对应位置获取数据
    currentScroll =
      getCurrentScrollPosition(currentKey) || getScrollPosition(currentKey)
    currentMinHeight = getMinScrollHeight(currentKey)
  } else {
    // 如果没有提供当前key，尝试从全局当前位置获取
    currentScroll = getCurrentScrollPosition("current") || 0
    currentMinHeight = getMinScrollHeight("current") || 0
  }

  // 获取目标tab的最小滚动高度和已保存的滚动位置
  const targetMinHeight = getMinScrollHeight(key)
  const targetSavedScroll = getScrollPosition(key)

  let targetScrollTop = 0

  // 应用滚动高度管理规则
  if (currentScroll < currentMinHeight) {
    // 规则1：当前tab滚动高度 < 最小滚动高度时，下一个tab滚动到当前tab的实际滚动高度
    targetScrollTop = currentScroll
  } else {
    // 规则2：当前tab滚动高度 ≥ 最小滚动高度时
    if (targetSavedScroll > 0) {
      // 有保存的高度
      if (targetSavedScroll >= targetMinHeight) {
        // 保存的高度 ≥ 最小滚动高度：滚动到保存的高度
        targetScrollTop = targetSavedScroll
      } else {
        // 保存的高度 < 最小滚动高度：滚动到最小滚动高度
        targetScrollTop = targetMinHeight
      }
    } else {
      // 没有保存的高度：滚动到最小滚动高度
      targetScrollTop = targetMinHeight
    }
  }

  // 如果目标滚动位置为0，则跳过
  if (targetScrollTop === 0) {
    return
  }

  wx.pageScrollTo({
    scrollTop: targetScrollTop,
    duration,
  })
}

/**
 * 获取滚动位置
 * @param {string} key 位置标识
 * @returns {number} 滚动位置
 */
function getScrollPosition(key) {
  return scrollPositions[key] || 0
}

/**
 * 设置最小滚动高度
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} minHeight 最小滚动高度
 */
function setMinScrollHeight(key, minHeight) {
  minScrollHeights[key] = minHeight
}

/**
 * 获取最小滚动高度
 * @param {string} key 位置标识
 * @returns {number} 最小滚动高度
 */
function getMinScrollHeight(key) {
  return minScrollHeights[key] || 0
}

/**
 * 设置当前滚动位置（用于智能恢复）
 * @param {string} key 位置标识
 * @param {number} scrollTop 当前滚动位置
 */
function setCurrentScrollPosition(key, scrollTop) {
  currentScrollPositions[key] = scrollTop
}

/**
 * 获取当前滚动位置
 * @param {string} key 位置标识
 * @returns {number} 当前滚动位置
 */
function getCurrentScrollPosition(key) {
  return currentScrollPositions[key] || 0
}

/**
 * 清除滚动位置
 * @param {string} key 位置标识，不传则清除所有
 */
function clearScrollPosition(key = null) {
  if (key === null) {
    // 清除所有滚动位置
    Object.keys(scrollPositions).forEach((k) => {
      delete scrollPositions[k]
    })
  } else {
    // 清除指定位置
    delete scrollPositions[key]
  }
}

/**
 * 清除最小滚动高度
 * @param {string} key 位置标识，不传则清除所有
 */
function clearMinScrollHeight(key = null) {
  if (key === null) {
    // 清除所有最小滚动高度
    Object.keys(minScrollHeights).forEach((k) => {
      delete minScrollHeights[k]
    })
  } else {
    // 清除指定最小滚动高度
    delete minScrollHeights[key]
  }
}

/**
 * 清除当前滚动位置
 * @param {string} key 位置标识，不传则清除所有
 */
function clearCurrentScrollPosition(key = null) {
  if (key === null) {
    // 清除所有当前滚动位置
    Object.keys(currentScrollPositions).forEach((k) => {
      delete currentScrollPositions[k]
    })
  } else {
    // 清除指定当前滚动位置
    delete currentScrollPositions[key]
  }
}

module.exports = {
  setScrollPosition,
  getScrollPosition,
  restoreScrollPosition,
  clearScrollPosition,
  setMinScrollHeight,
  getMinScrollHeight,
  setCurrentScrollPosition,
  getCurrentScrollPosition,
  clearMinScrollHeight,
  clearCurrentScrollPosition,
}
