/**
 * 滚动位置管理服务
 * 提供页面滚动位置的记录、恢复功能
 *
 * 功能特性：
 * - 简洁的API设计，只传递key和滚动位置
 * - 支持多页面独立管理滚动位置
 * - 内存存储，页面生命周期内有效
 * - 最小侵入性集成
 */

// 内存存储滚动位置数据
const scrollPositions = {}

/**
 * 记录滚动位置
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} scrollTop 滚动位置
 */
function setScrollPosition(key, scrollTop) {
  scrollPositions[key] = scrollTop
}

/**
 * 恢复滚动位置
 * @param {string} key 位置标识
 * @param {Object} options 恢复选项
 * @param {number} options.duration 动画时长，默认0（无动画）
 */
function restoreScrollPosition(key, options = {}) {
  const { duration = 0 } = options
  const savedScrollTop = scrollPositions[key] || 0

  // 如果滚动位置为0，则跳过
  if (savedScrollTop === 0) {
    return
  }

  wx.pageScrollTo({
    scrollTop: savedScrollTop,
    duration,
  })
}

/**
 * 获取滚动位置
 * @param {string} key 位置标识
 * @returns {number} 滚动位置
 */
function getScrollPosition(key) {
  return scrollPositions[key] || 0
}

/**
 * 清除滚动位置
 * @param {string} key 位置标识，不传则清除所有
 */
function clearScrollPosition(key = null) {
  if (key === null) {
    // 清除所有滚动位置
    Object.keys(scrollPositions).forEach((k) => {
      delete scrollPositions[k]
    })
  } else {
    // 清除指定位置
    delete scrollPositions[key]
  }
}

module.exports = {
  setScrollPosition,
  getScrollPosition,
  restoreScrollPosition,
  clearScrollPosition,
}
