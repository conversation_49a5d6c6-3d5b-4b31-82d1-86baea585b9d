/**
 * 滚动位置管理服务
 * 提供页面滚动位置的记录、恢复功能
 *
 * 功能特性：
 * - 简洁的API设计，只传递key和滚动位置
 * - 支持多页面独立管理滚动位置
 * - 内存存储，页面生命周期内有效
 * - 最小侵入性集成
 * - 支持最小滚动高度管理，智能处理tab切换滚动逻辑
 */

// 内存存储滚动位置数据
const scrollPositions = {}

// 内存存储最小滚动高度配置
const minScrollHeights = {}

/**
 * 记录滚动位置
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} scrollTop 滚动位置
 */
function setScrollPosition(key, scrollTop) {
  scrollPositions[key] = scrollTop
}

/**
 * 恢复滚动位置
 * @param {string} key 位置标识
 * @param {Object} options 恢复选项
 * @param {number} options.duration 动画时长，默认0（无动画）
 */
function restoreScrollPosition(key, options = {}) {
  const { duration = 0 } = options
  const savedScrollTop = scrollPositions[key] || 0

  // 如果滚动位置为0，则跳过
  if (savedScrollTop === 0) {
    return
  }

  wx.pageScrollTo({
    scrollTop: savedScrollTop,
    duration,
  })
}

/**
 * 获取滚动位置
 * @param {string} key 位置标识
 * @returns {number} 滚动位置
 */
function getScrollPosition(key) {
  return scrollPositions[key] || 0
}

/**
 * 设置最小滚动高度
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} minHeight 最小滚动高度
 */
function setMinScrollHeight(key, minHeight) {
  minScrollHeights[key] = minHeight
}

/**
 * 获取最小滚动高度
 * @param {string} key 位置标识
 * @returns {number} 最小滚动高度
 */
function getMinScrollHeight(key) {
  return minScrollHeights[key] || 0
}

/**
 * 智能恢复滚动位置（支持最小滚动高度管理）
 * @param {string} currentTabKey 当前tab的key
 * @param {string} targetTabKey 目标tab的key
 * @param {Object} options 恢复选项
 * @param {number} options.duration 动画时长，默认0（无动画）
 * @param {number} options.currentScrollTop 当前tab的实际滚动位置（可选，不传则自动获取）
 */
function smartRestoreScrollPosition(currentTabKey, targetTabKey, options = {}) {
  const { duration = 0, currentScrollTop } = options

  // 获取当前tab的滚动位置（优先使用传入的值）
  const currentScroll =
    currentScrollTop !== undefined
      ? currentScrollTop
      : getScrollPosition(currentTabKey)

  // 获取当前tab和目标tab的最小滚动高度
  const currentMinHeight = getMinScrollHeight(currentTabKey)
  const targetMinHeight = getMinScrollHeight(targetTabKey)

  // 获取目标tab已保存的滚动位置
  const targetSavedScroll = getScrollPosition(targetTabKey)

  let targetScrollTop = 0

  // 应用滚动高度管理规则
  if (currentScroll < currentMinHeight) {
    // 规则1：当前tab滚动高度 < 最小滚动高度时，下一个tab滚动到当前tab的实际滚动高度
    targetScrollTop = currentScroll
  } else {
    // 规则2：当前tab滚动高度 ≥ 最小滚动高度时
    if (targetSavedScroll > 0) {
      // 有保存的高度
      if (targetSavedScroll >= targetMinHeight) {
        // 保存的高度 ≥ 最小滚动高度：滚动到保存的高度
        targetScrollTop = targetSavedScroll
      } else {
        // 保存的高度 < 最小滚动高度：滚动到最小滚动高度
        targetScrollTop = targetMinHeight
      }
    } else {
      // 没有保存的高度：滚动到最小滚动高度
      targetScrollTop = targetMinHeight
    }
  }

  // 如果目标滚动位置为0，则跳过
  if (targetScrollTop === 0) {
    return
  }

  wx.pageScrollTo({
    scrollTop: targetScrollTop,
    duration,
  })
}

/**
 * 清除滚动位置
 * @param {string} key 位置标识，不传则清除所有
 */
function clearScrollPosition(key = null) {
  if (key === null) {
    // 清除所有滚动位置
    Object.keys(scrollPositions).forEach((k) => {
      delete scrollPositions[k]
    })
  } else {
    // 清除指定位置
    delete scrollPositions[key]
  }
}

/**
 * 清除最小滚动高度
 * @param {string} key 位置标识，不传则清除所有
 */
function clearMinScrollHeight(key = null) {
  if (key === null) {
    // 清除所有最小滚动高度
    Object.keys(minScrollHeights).forEach((k) => {
      delete minScrollHeights[k]
    })
  } else {
    // 清除指定最小滚动高度
    delete minScrollHeights[key]
  }
}

module.exports = {
  setScrollPosition,
  getScrollPosition,
  restoreScrollPosition,
  clearScrollPosition,
  setMinScrollHeight,
  getMinScrollHeight,
  smartRestoreScrollPosition,
  clearMinScrollHeight,
}
